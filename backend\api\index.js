import express from "express";
import cors from "cors";
import dotenv from "dotenv";

dotenv.config();

// Import database connection
import { sequelize, testConnection } from "../src/config/database.js";

// Import routes
import authRoutes from "../src/routes/auth.routes.js";
import userRoutes from "../src/routes/user.routes.js";
import companyRoutes from "../src/routes/company.routes.js";
import projectRoutes from "../src/routes/project.routes.js";
import taskRoutes from "../src/routes/task.routes.js";
import attendanceRoutes from "../src/routes/attendance.routes.js";
import leaveRoutes from "../src/routes/leave.routes.js";
import evaluationRoutes from "../src/routes/evaluation.routes.js";
import analyticsRoutes from "../src/routes/analytics.routes.js";
import waitlistRoutes from "../src/routes/waitlist.routes.js";

const app = express();
const PORT = process.env.PORT || 3000;

// CORS config
app.use(cors());

// app.options('*', cors()); // Preflight for all routes

// Other middlewares
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Middleware
// Routes
app.use("/auth", authRoutes);
app.use("/users", userRoutes);
app.use("/companies", companyRoutes);
app.use("/projects", projectRoutes);
app.use("/tasks", taskRoutes);
app.use("/attendance", attendanceRoutes);
app.use("/leaves", leaveRoutes);
app.use("/evaluations", evaluationRoutes);
app.use("/analytics", analyticsRoutes);
app.use("/waitlist", waitlistRoutes);

// Root route
app.get("/", (req, res) => {
  res.json({ message: "Welcome to TeamCheck API" });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    message: "Something went wrong!",
    error:
      process.env.NODE_ENV === "development"
        ? err.message
        : "Internal Server Error",
  });
});

// Start server
const startServer = async () => {
  try {
    // Test database connection
    await testConnection();

    // Sync models with database (create tables if they don't exist)
    console.log("Syncing models with database...");
    await sequelize.sync();
    console.log("Database sync completed successfully!");

    // Start the server
    app.listen(PORT, () => {
      console.log(`Server is running on port ${PORT}`);
    });
  } catch (error) {
    console.error("Failed to start server:", error);
    process.exit(1);
  }
};

// Run the server
// startServer();

export default app;
