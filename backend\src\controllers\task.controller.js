const { sequelize } = require('../config/database');

// We'll need to create the Task model first, let's create a basic implementation
const Task = {
  // Placeholder methods - we'll implement these with Sequelize models
  findAll: async (filters) => {
    // TODO: Implement with Sequelize
    return [];
  },

  findByPk: async (id) => {
    // TODO: Implement with Sequelize
    return null;
  },

  create: async (data) => {
    // TODO: Implement with Sequelize
    return data;
  },

  update: async (id, data) => {
    // TODO: Implement with Sequelize
    return true;
  },

  destroy: async (id) => {
    // TODO: Implement with Sequelize
    return true;
  },

  getTaskWithDetails: async (id) => {
    // TODO: Implement with Sequelize
    return null;
  },

  addAssignee: async (taskId, userId) => {
    // TODO: Implement with Sequelize
    return true;
  },

  removeAssignee: async (taskId, userId) => {
    // TODO: Implement with Sequelize
    return true;
  },

  addComment: async (taskId, userId, text) => {
    // TODO: Implement with Sequelize
    return { id: 'comment-id', text, userId, createdAt: new Date() };
  },

  addChecklistItem: async (taskId, text) => {
    // TODO: Implement with Sequelize
    return { id: 'item-id', text, completed: false };
  },

  updateChecklistItem: async (itemId, completed) => {
    // TODO: Implement with Sequelize
    return { id: itemId, completed };
  }
};

// Get all tasks
const getTasks = async (req, res) => {
  try {
    const { companyId, projectId, status, assignedTo } = req.query;

    // Build filter object
    const filters = {};

    // If user is not admin, only show tasks from their company
    if (req.user.role !== 'admin') {
      filters.companyId = req.user.companyId;
    } else if (companyId) {
      filters.companyId = companyId;
    }

    if (projectId) filters.projectId = projectId;
    if (status) filters.status = status;
    if (assignedTo) filters.assignedTo = assignedTo;

    const tasks = await Task.findAll(filters);

    res.status(200).json({
      success: true,
      data: tasks
    });
  } catch (error) {
    console.error('Get tasks error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch tasks'
    });
  }
};

// Get task by ID
const getTaskById = async (req, res) => {
  try {
    const { id } = req.params;

    const task = await Task.getTaskWithDetails(id);

    if (!task) {
      return res.status(404).json({
        success: false,
        message: 'Task not found'
      });
    }

    // Check if user has access to this task
    if (req.user.role !== 'admin' && req.user.companyId !== task.companyId) {
      return res.status(403).json({
        success: false,
        message: 'Access denied to this task'
      });
    }

    res.status(200).json({
      success: true,
      data: task
    });
  } catch (error) {
    console.error('Get task by ID error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch task'
    });
  }
};

// Create new task
const createTask = async (req, res) => {
  try {
    const { title, description, projectId, companyId, dueDate, columnId } = req.body;
    const userId = req.user.id;

    // Validate required fields
    if (!title || !projectId || !companyId) {
      return res.status(400).json({
        success: false,
        message: 'Title, project ID, and company ID are required'
      });
    }

    // Check if user has access to this company
    if (req.user.role !== 'admin' && req.user.companyId !== companyId) {
      return res.status(403).json({
        success: false,
        message: 'Access denied to create task in this company'
      });
    }

    // Create task
    const task = await Task.create({
      title,
      description,
      projectId,
      companyId,
      createdBy: userId,
      dueDate,
      columnId
    });

    res.status(201).json({
      success: true,
      data: task,
      message: 'Task created successfully'
    });
  } catch (error) {
    console.error('Create task error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create task'
    });
  }
};

// Update task
const updateTask = async (req, res) => {
  try {
    const { id } = req.params;
    const { title, description, status, dueDate } = req.body;

    const task = await Task.findByPk(id);

    if (!task) {
      return res.status(404).json({
        success: false,
        message: 'Task not found'
      });
    }

    // Check permissions
    const canUpdate = req.user.role === 'admin' ||
                     task.createdBy === req.user.id ||
                     (req.user.role === 'manager' && req.user.companyId === task.companyId);

    if (!canUpdate) {
      return res.status(403).json({
        success: false,
        message: 'Insufficient permissions to update this task'
      });
    }

    // Update task
    await Task.update(id, {
      title: title || task.title,
      description: description !== undefined ? description : task.description,
      status: status || task.status,
      dueDate: dueDate !== undefined ? dueDate : task.dueDate
    });

    // Get updated task
    const updatedTask = await Task.getTaskWithDetails(id);

    res.status(200).json({
      success: true,
      data: updatedTask,
      message: 'Task updated successfully'
    });
  } catch (error) {
    console.error('Update task error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update task'
    });
  }
};

// Delete task
const deleteTask = async (req, res) => {
  try {
    const { id } = req.params;

    const task = await Task.findByPk(id);

    if (!task) {
      return res.status(404).json({
        success: false,
        message: 'Task not found'
      });
    }

    // Check permissions
    const canDelete = req.user.role === 'admin' ||
                     task.createdBy === req.user.id ||
                     (req.user.role === 'manager' && req.user.companyId === task.companyId);

    if (!canDelete) {
      return res.status(403).json({
        success: false,
        message: 'Insufficient permissions to delete this task'
      });
    }

    await Task.destroy(id);

    res.status(200).json({
      success: true,
      message: 'Task deleted successfully'
    });
  } catch (error) {
    console.error('Delete task error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete task'
    });
  }
};

// Move task (for Kanban)
const moveTask = async (req, res) => {
  try {
    const { id } = req.params;
    const { columnId } = req.body;

    if (!columnId) {
      return res.status(400).json({
        success: false,
        message: 'Column ID is required'
      });
    }

    const task = await Task.findByPk(id);

    if (!task) {
      return res.status(404).json({
        success: false,
        message: 'Task not found'
      });
    }

    // Check permissions
    const canMove = req.user.role === 'admin' ||
                   task.createdBy === req.user.id ||
                   (req.user.companyId === task.companyId);

    if (!canMove) {
      return res.status(403).json({
        success: false,
        message: 'Insufficient permissions to move this task'
      });
    }

    // Update task column
    await Task.update(id, { columnId });

    res.status(200).json({
      success: true,
      message: 'Task moved successfully'
    });
  } catch (error) {
    console.error('Move task error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to move task'
    });
  }
};

// Add comment to task
const addTaskComment = async (req, res) => {
  try {
    const { id } = req.params;
    const { text } = req.body;
    const userId = req.user.id;

    if (!text) {
      return res.status(400).json({
        success: false,
        message: 'Comment text is required'
      });
    }

    const task = await Task.findByPk(id);

    if (!task) {
      return res.status(404).json({
        success: false,
        message: 'Task not found'
      });
    }

    // Check if user has access to this task
    if (req.user.role !== 'admin' && req.user.companyId !== task.companyId) {
      return res.status(403).json({
        success: false,
        message: 'Access denied to comment on this task'
      });
    }

    const comment = await Task.addComment(id, userId, text);

    res.status(201).json({
      success: true,
      data: comment,
      message: 'Comment added successfully'
    });
  } catch (error) {
    console.error('Add task comment error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to add comment'
    });
  }
};

// Add checklist item to task
const addChecklistItem = async (req, res) => {
  try {
    const { id } = req.params;
    const { text } = req.body;

    if (!text) {
      return res.status(400).json({
        success: false,
        message: 'Checklist item text is required'
      });
    }

    const task = await Task.findByPk(id);

    if (!task) {
      return res.status(404).json({
        success: false,
        message: 'Task not found'
      });
    }

    // Check permissions
    const canAddItem = req.user.role === 'admin' ||
                      task.createdBy === req.user.id ||
                      (req.user.companyId === task.companyId);

    if (!canAddItem) {
      return res.status(403).json({
        success: false,
        message: 'Insufficient permissions to add checklist items to this task'
      });
    }

    const item = await Task.addChecklistItem(id, text);

    res.status(201).json({
      success: true,
      data: item,
      message: 'Checklist item added successfully'
    });
  } catch (error) {
    console.error('Add checklist item error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to add checklist item'
    });
  }
};

// Update checklist item
const updateChecklistItem = async (req, res) => {
  try {
    const { id, itemId } = req.params;
    const { completed } = req.body;

    if (completed === undefined) {
      return res.status(400).json({
        success: false,
        message: 'Completed status is required'
      });
    }

    const task = await Task.findByPk(id);

    if (!task) {
      return res.status(404).json({
        success: false,
        message: 'Task not found'
      });
    }

    // Check permissions
    const canUpdate = req.user.role === 'admin' ||
                     task.createdBy === req.user.id ||
                     (req.user.companyId === task.companyId);

    if (!canUpdate) {
      return res.status(403).json({
        success: false,
        message: 'Insufficient permissions to update checklist items for this task'
      });
    }

    const item = await Task.updateChecklistItem(itemId, completed);

    res.status(200).json({
      success: true,
      data: item,
      message: 'Checklist item updated successfully'
    });
  } catch (error) {
    console.error('Update checklist item error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update checklist item'
    });
  }
};

module.exports = {
  getTasks,
  getTaskById,
  createTask,
  updateTask,
  deleteTask,
  moveTask,
  addTaskComment,
  addChecklistItem,
  updateChecklistItem
};
