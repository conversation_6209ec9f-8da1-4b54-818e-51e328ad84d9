const { User } = require('../models');
const { generateToken } = require('../utils/jwt');

// Register a new user
const register = async (req, res) => {
  try {
    const { email, password, name, position, companyId } = req.body;

    // Check if user already exists
    const existingUser = await User.findByEmail(email);
    if (existingUser) {
      return res.status(400).json({ message: 'User already exists with this email' });
    }

    // Create new user
    const user = await User.create({
      email,
      password,
      name,
      position,
      role: 'employee', // Default role for new users
      companyId: companyId || null, // Allow null companyId
      dateJoined: new Date()
    });

    // Get user data without password
    const userData = user.toJSON();
    delete userData.password;

    // Generate JWT token
    const token = generateToken(userData);

    res.status(201).json({
      user: userData,
      token
    });
  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({ message: 'Failed to register user' });
  }
};

// Login user
const login = async (req, res) => {
  try {
    const { email, password } = req.body;

    // Find user by email
    const user = await User.findByEmail(email);
    if (!user) {
      return res.status(401).json({ message: 'Invalid email or password' });
    }

    // Verify password
    const isPasswordValid = await user.comparePassword(password);
    if (!isPasswordValid) {
      return res.status(401).json({ message: 'Invalid email or password' });
    }

    // Get user data without password
    const userData = user.toJSON();
    delete userData.password;

    // Generate JWT token
    const token = generateToken(userData);

    res.status(200).json({
      user: userData,
      token
    });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ message: 'Failed to login' });
  }
};

// Get current user
const getCurrentUser = async (req, res) => {
  try {
    const userId = req.user.id;

    // Find user by ID
    const user = await User.findByPk(userId, {
      attributes: { exclude: ['password'] }
    });

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    res.status(200).json({ user });
  } catch (error) {
    console.error('Get current user error:', error);
    res.status(500).json({ message: 'Failed to get user information' });
  }
};

// Verify email (placeholder for email verification functionality)
const verifyEmail = async (req, res) => {
  try {
    const { token } = req.body;

    // Implement email verification logic here
    // This is a placeholder for now

    res.status(200).json({ success: true, message: 'Email verified successfully' });
  } catch (error) {
    console.error('Email verification error:', error);
    res.status(500).json({ message: 'Failed to verify email' });
  }
};

// Request password reset (placeholder for password reset functionality)
const forgotPassword = async (req, res) => {
  try {
    const { email } = req.body;

    // Check if user exists
    const user = await User.findByEmail(email);
    if (!user) {
      // For security reasons, don't reveal that the email doesn't exist
      return res.status(200).json({ success: true, message: 'If your email is registered, you will receive a password reset link' });
    }

    // Implement password reset logic here
    // This is a placeholder for now

    res.status(200).json({ success: true, message: 'If your email is registered, you will receive a password reset link' });
  } catch (error) {
    console.error('Forgot password error:', error);
    res.status(500).json({ message: 'Failed to process password reset request' });
  }
};

// Reset password (placeholder for password reset functionality)
const resetPassword = async (req, res) => {
  try {
    const { token, password } = req.body;

    // Implement password reset logic here
    // This is a placeholder for now

    res.status(200).json({ success: true, message: 'Password reset successfully' });
  } catch (error) {
    console.error('Reset password error:', error);
    res.status(500).json({ message: 'Failed to reset password' });
  }
};

module.exports = {
  register,
  login,
  getCurrentUser,
  verifyEmail,
  forgotPassword,
  resetPassword
};
