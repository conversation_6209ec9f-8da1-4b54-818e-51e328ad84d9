const pool = require('../config/db');
const { v4: uuidv4 } = require('uuid');

class Evaluation {
  constructor(evaluation) {
    this.id = evaluation.id || uuidv4();
    this.workspaceId = evaluation.workspaceId;
    this.type = evaluation.type;
    this.employeeId = evaluation.employeeId;
    this.evaluatorId = evaluation.evaluatorId;
    this.projectId = evaluation.projectId || null;
    this.date = evaluation.date || new Date().toISOString().split('T')[0]; // YYYY-MM-DD
    this.status = evaluation.status || 'in progress';
    this.performanceRating = evaluation.performanceRating || null;
    this.teamworkRating = evaluation.teamworkRating || null;
    this.communicationRating = evaluation.communicationRating || null;
    this.initiativeRating = evaluation.initiativeRating || null;
    this.reliabilityRating = evaluation.reliabilityRating || null;
    this.feedback = evaluation.feedback || null;
    this.createdAt = evaluation.createdAt || new Date();
    this.completedAt = evaluation.completedAt || null;
    this.updatedAt = evaluation.updatedAt || new Date();
  }

  // Create a new evaluation
  async create() {
    const query = `
      INSERT INTO evaluations (
        id, workspaceId, type, employeeId, evaluatorId, projectId, date, 
        status, performanceRating, teamworkRating, communicationRating, 
        initiativeRating, reliabilityRating, feedback, createdAt, 
        completedAt, updatedAt
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const values = [
      this.id, this.workspaceId, this.type, this.employeeId, this.evaluatorId,
      this.projectId, this.date, this.status, this.performanceRating,
      this.teamworkRating, this.communicationRating, this.initiativeRating,
      this.reliabilityRating, this.feedback, this.createdAt, this.completedAt,
      this.updatedAt
    ];

    try {
      const [result] = await pool.query(query, values);
      return { id: this.id, ...this };
    } catch (error) {
      throw error;
    }
  }

  // Find evaluation by ID
  static async findById(id) {
    try {
      const [rows] = await pool.query('SELECT * FROM evaluations WHERE id = ?', [id]);
      return rows.length ? rows[0] : null;
    } catch (error) {
      throw error;
    }
  }

  // Get all evaluations
  static async findAll(filters = {}) {
    try {
      let query = 'SELECT * FROM evaluations';
      const values = [];
      
      // Add filters if provided
      if (Object.keys(filters).length > 0) {
        const conditions = [];
        
        if (filters.workspaceId) {
          conditions.push('workspaceId = ?');
          values.push(filters.workspaceId);
        }
        
        if (filters.type) {
          conditions.push('type = ?');
          values.push(filters.type);
        }
        
        if (filters.employeeId) {
          conditions.push('employeeId = ?');
          values.push(filters.employeeId);
        }
        
        if (filters.evaluatorId) {
          conditions.push('evaluatorId = ?');
          values.push(filters.evaluatorId);
        }
        
        if (filters.projectId) {
          conditions.push('projectId = ?');
          values.push(filters.projectId);
        }
        
        if (filters.status) {
          conditions.push('status = ?');
          values.push(filters.status);
        }
        
        if (filters.startDate) {
          conditions.push('date >= ?');
          values.push(filters.startDate);
        }
        
        if (filters.endDate) {
          conditions.push('date <= ?');
          values.push(filters.endDate);
        }
        
        if (conditions.length > 0) {
          query += ' WHERE ' + conditions.join(' AND ');
        }
      }
      
      // Add order by
      query += ' ORDER BY date DESC, createdAt DESC';
      
      const [rows] = await pool.query(query, values);
      return rows;
    } catch (error) {
      throw error;
    }
  }

  // Update evaluation
  static async update(id, evaluationData) {
    try {
      // Don't allow updating the ID
      delete evaluationData.id;
      
      // Add updatedAt timestamp
      evaluationData.updatedAt = new Date();
      
      // Build the query dynamically based on the fields to update
      const fields = Object.keys(evaluationData)
        .map(key => `${key} = ?`)
        .join(', ');
      
      const values = [...Object.values(evaluationData), id];
      
      const query = `UPDATE evaluations SET ${fields} WHERE id = ?`;
      
      const [result] = await pool.query(query, values);
      return result.affectedRows > 0;
    } catch (error) {
      throw error;
    }
  }

  // Delete evaluation
  static async delete(id) {
    try {
      // First delete any associated goals
      await pool.query('DELETE FROM evaluation_goals WHERE evaluationId = ?', [id]);
      
      // Then delete the evaluation
      const [result] = await pool.query('DELETE FROM evaluations WHERE id = ?', [id]);
      return result.affectedRows > 0;
    } catch (error) {
      throw error;
    }
  }

  // Complete evaluation
  static async complete(id, evaluationData) {
    try {
      // Set status to completed and add completedAt timestamp
      const data = {
        ...evaluationData,
        status: 'completed',
        completedAt: new Date()
      };
      
      // Update the evaluation
      const updated = await this.update(id, data);
      
      // Add goals if provided
      if (updated && evaluationData.goals && Array.isArray(evaluationData.goals)) {
        for (const goalText of evaluationData.goals) {
          await this.addGoal(id, goalText);
        }
      }
      
      return await this.getEvaluationWithDetails(id);
    } catch (error) {
      throw error;
    }
  }

  // Add goal to evaluation
  static async addGoal(evaluationId, goal) {
    try {
      const goalId = uuidv4();
      
      const query = `
        INSERT INTO evaluation_goals (id, evaluationId, goal)
        VALUES (?, ?, ?)
      `;
      
      const [result] = await pool.query(query, [goalId, evaluationId, goal]);
      
      if (result.affectedRows > 0) {
        return {
          id: goalId,
          evaluationId,
          goal
        };
      }
      
      return null;
    } catch (error) {
      throw error;
    }
  }

  // Get goals for an evaluation
  static async getGoals(evaluationId) {
    try {
      const query = `
        SELECT *
        FROM evaluation_goals
        WHERE evaluationId = ?
      `;
      
      const [rows] = await pool.query(query, [evaluationId]);
      return rows;
    } catch (error) {
      throw error;
    }
  }

  // Get evaluation with details (including employee, evaluator, project, goals)
  static async getEvaluationWithDetails(id) {
    try {
      // Get evaluation
      const evaluation = await this.findById(id);
      if (!evaluation) return null;
      
      // Get employee details
      const [employeeRows] = await pool.query(
        'SELECT id, name, email, position, avatar FROM users WHERE id = ?',
        [evaluation.employeeId]
      );
      
      // Get evaluator details
      const [evaluatorRows] = await pool.query(
        'SELECT id, name, email, position, avatar FROM users WHERE id = ?',
        [evaluation.evaluatorId]
      );
      
      // Get project details if applicable
      let project = null;
      if (evaluation.projectId) {
        const [projectRows] = await pool.query(
          'SELECT id, title, description FROM projects WHERE id = ?',
          [evaluation.projectId]
        );
        
        if (projectRows.length > 0) {
          project = projectRows[0];
        }
      }
      
      // Get goals
      const goals = await this.getGoals(id);
      
      return {
        ...evaluation,
        employee: employeeRows.length > 0 ? employeeRows[0] : null,
        evaluator: evaluatorRows.length > 0 ? evaluatorRows[0] : null,
        project,
        goals
      };
    } catch (error) {
      throw error;
    }
  }
}

module.exports = Evaluation;
