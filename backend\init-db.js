const { initializeDatabase } = require('./src/config/init-db');

// Parse command line arguments
const args = process.argv.slice(2);
const options = {
  force: args.includes('--force'),
  alter: args.includes('--alter')
};

// Run the database initialization
console.log(`Initializing database with options: ${JSON.stringify(options)}`);
initializeDatabase(options)
  .then(() => {
    console.log('Database initialization completed successfully!');
    process.exit(0);
  })
  .catch(err => {
    console.error('Failed to initialize database:', err);
    process.exit(1);
  });
