const express = require('express');
const router = express.Router();
const companyController = require('../controllers/company.controller');
const { authenticate, authorize, companyAccess } = require('../middleware/auth.middleware');

// Get all companies for current user
router.get('/', authenticate, companyController.getCompanies);

// Create new company
router.post('/', authenticate, companyController.createCompany);

// Get company by ID
router.get('/:id', authenticate, companyAccess, companyController.getCompanyById);

// Update company
router.put('/:id', authenticate, companyAccess, companyController.updateCompany);

// Delete company
router.delete('/:id', authenticate, companyAccess, companyController.deleteCompany);

// Get all users in company
router.get('/:id/users', authenticate, companyAccess, companyController.getCompanyUsers);

// Invite user to company
router.post('/:id/invite', authenticate, companyAccess, authorize(['admin', 'manager']), companyController.inviteUserToCompany);

module.exports = router;
