const { sequelize } = require('../config/database');
const User = require('./user.model');
const Company = require('./company.model');
const {
  Project,
  ProjectTeamMember,
  ProjectColumn,
  ProjectMilestone
} = require('./project.model');

// Define associations
// User <-> Company
User.belongsTo(Company, { foreignKey: 'companyId' });
Company.hasMany(User, { foreignKey: 'companyId' });

// User <-> Project (creator)
Project.belongsTo(User, { foreignKey: 'createdBy', as: 'Creator' });
User.hasMany(Project, { foreignKey: 'createdBy', as: 'CreatedProjects' });

// Project <-> Company
Project.belongsTo(Company, { foreignKey: 'companyId' });
Company.hasMany(Project, { foreignKey: 'companyId' });

// Project <-> ProjectTeamMember
Project.hasMany(ProjectTeamMember, { foreignKey: 'projectId' });
ProjectTeamMember.belongsTo(Project, { foreignKey: 'projectId' });

// User <-> ProjectTeamMember
User.hasMany(ProjectTeamMember, { foreignKey: 'userId' });
ProjectTeamMember.belongsTo(User, { foreignKey: 'userId' });

// Project <-> ProjectColumn
Project.hasMany(ProjectColumn, { foreignKey: 'projectId' });
ProjectColumn.belongsTo(Project, { foreignKey: 'projectId' });

// Project <-> ProjectMilestone
Project.hasMany(ProjectMilestone, { foreignKey: 'projectId' });
ProjectMilestone.belongsTo(Project, { foreignKey: 'projectId' });

// Export models
module.exports = {
  sequelize,
  User,
  Company,
  Project,
  ProjectTeamMember,
  ProjectColumn,
  ProjectMilestone
};
