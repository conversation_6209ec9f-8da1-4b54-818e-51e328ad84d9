const express = require('express');
const router = express.Router();
const evaluationController = require('../controllers/evaluation.controller');
const { authenticate, authorize } = require('../middleware/auth.middleware');

// Get evaluations
router.get('/', authenticate, evaluationController.getEvaluations);

// Create evaluation
router.post('/', authenticate, authorize(['admin', 'manager']), evaluationController.createEvaluation);

// Get evaluation by ID
router.get('/:id', authenticate, evaluationController.getEvaluationById);

// Update evaluation
router.put('/:id', authenticate, evaluationController.updateEvaluation);

// Delete evaluation
router.delete('/:id', authenticate, authorize(['admin', 'manager']), evaluationController.deleteEvaluation);

// Complete evaluation
router.put('/:id/complete', authenticate, evaluationController.completeEvaluation);

module.exports = router;
