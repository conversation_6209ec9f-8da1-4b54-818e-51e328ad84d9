const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');
const { v4: uuidv4 } = require('uuid');

const Company = sequelize.define('Company', {
  id: {
    type: DataTypes.STRING(36),
    primaryKey: true,
    defaultValue: () => uuidv4()
  },
  name: {
    type: DataTypes.STRING(255),
    allowNull: false
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  timezone: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  country: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  logo: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  ownerId: {
    type: DataTypes.STRING(36),
    allowNull: false
  },
  checkInHoursStart: {
    type: DataTypes.STRING(5),
    allowNull: true
  },
  checkInHoursEnd: {
    type: DataTypes.STRING(5),
    allowNull: true
  },
  messageFormat: {
    type: DataTypes.STRING(255),
    allowNull: true
  }
}, {
  tableName: 'companies',
  timestamps: true,
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
});

// Class method to find companies for a user
Company.findForUser = async function(userId) {
  const { Op } = require('sequelize');
  const User = require('./user.model');
  
  return await Company.findAll({
    where: {
      [Op.or]: [
        { ownerId: userId },
        { '$Users.id$': userId }
      ]
    },
    include: [{
      model: User,
      as: 'Users',
      attributes: [],
      required: false
    }]
  });
};

// Get users in a company
Company.getUsers = async function(companyId) {
  const User = require('./user.model');
  
  return await User.findAll({
    where: { companyId },
    attributes: { exclude: ['password'] }
  });
};

module.exports = Company;
