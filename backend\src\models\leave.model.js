const pool = require('../config/db');
const { v4: uuidv4 } = require('uuid');

class Leave {
  constructor(leave) {
    this.id = leave.id || uuidv4();
    this.workspaceId = leave.workspaceId;
    this.userId = leave.userId;
    this.startDate = leave.startDate;
    this.endDate = leave.endDate;
    this.reason = leave.reason || null;
    this.status = leave.status || 'pending';
    this.approvedBy = leave.approvedBy || null;
    this.createdAt = leave.createdAt || new Date();
    this.updatedAt = leave.updatedAt || new Date();
  }

  // Create a new leave request
  async create() {
    const query = `
      INSERT INTO leaves (
        id, workspaceId, userId, startDate, endDate, 
        reason, status, approvedBy, createdAt, updatedAt
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const values = [
      this.id, this.workspaceId, this.userId, this.startDate, this.endDate,
      this.reason, this.status, this.approvedBy, this.createdAt, this.updatedAt
    ];

    try {
      const [result] = await pool.query(query, values);
      return { id: this.id, ...this };
    } catch (error) {
      throw error;
    }
  }

  // Find leave request by ID
  static async findById(id) {
    try {
      const [rows] = await pool.query('SELECT * FROM leaves WHERE id = ?', [id]);
      return rows.length ? rows[0] : null;
    } catch (error) {
      throw error;
    }
  }

  // Get all leave requests
  static async findAll(filters = {}) {
    try {
      let query = 'SELECT * FROM leaves';
      const values = [];
      
      // Add filters if provided
      if (Object.keys(filters).length > 0) {
        const conditions = [];
        
        if (filters.workspaceId) {
          conditions.push('workspaceId = ?');
          values.push(filters.workspaceId);
        }
        
        if (filters.userId) {
          conditions.push('userId = ?');
          values.push(filters.userId);
        }
        
        if (filters.status) {
          conditions.push('status = ?');
          values.push(filters.status);
        }
        
        if (filters.startDate) {
          conditions.push('startDate >= ?');
          values.push(filters.startDate);
        }
        
        if (filters.endDate) {
          conditions.push('endDate <= ?');
          values.push(filters.endDate);
        }
        
        if (conditions.length > 0) {
          query += ' WHERE ' + conditions.join(' AND ');
        }
      }
      
      // Add order by
      query += ' ORDER BY createdAt DESC';
      
      const [rows] = await pool.query(query, values);
      return rows;
    } catch (error) {
      throw error;
    }
  }

  // Update leave request
  static async update(id, leaveData) {
    try {
      // Don't allow updating the ID
      delete leaveData.id;
      
      // Add updatedAt timestamp
      leaveData.updatedAt = new Date();
      
      // Build the query dynamically based on the fields to update
      const fields = Object.keys(leaveData)
        .map(key => `${key} = ?`)
        .join(', ');
      
      const values = [...Object.values(leaveData), id];
      
      const query = `UPDATE leaves SET ${fields} WHERE id = ?`;
      
      const [result] = await pool.query(query, values);
      return result.affectedRows > 0;
    } catch (error) {
      throw error;
    }
  }

  // Delete leave request
  static async delete(id) {
    try {
      const [result] = await pool.query('DELETE FROM leaves WHERE id = ?', [id]);
      return result.affectedRows > 0;
    } catch (error) {
      throw error;
    }
  }

  // Approve leave request
  static async approve(id, approvedBy) {
    try {
      const query = `
        UPDATE leaves
        SET status = 'approved', approvedBy = ?, updatedAt = ?
        WHERE id = ?
      `;
      
      const [result] = await pool.query(query, [approvedBy, new Date(), id]);
      
      if (result.affectedRows > 0) {
        // Update attendance records for the leave period
        const leave = await this.findById(id);
        
        if (leave) {
          // Get all dates between startDate and endDate
          const dates = this.getDatesBetween(leave.startDate, leave.endDate);
          
          // For each date, create or update attendance record with 'on leave' status
          for (const date of dates) {
            const [existingRecord] = await pool.query(
              'SELECT id FROM attendance WHERE userId = ? AND date = ?',
              [leave.userId, date]
            );
            
            if (existingRecord.length > 0) {
              // Update existing record
              await pool.query(
                'UPDATE attendance SET status = ?, updatedAt = ? WHERE id = ?',
                ['on leave', new Date(), existingRecord[0].id]
              );
            } else {
              // Create new record
              const attendanceId = uuidv4();
              await pool.query(
                `INSERT INTO attendance (
                  id, workspaceId, userId, date, status, createdAt, updatedAt
                ) VALUES (?, ?, ?, ?, ?, ?, ?)`,
                [attendanceId, leave.workspaceId, leave.userId, date, 'on leave', new Date(), new Date()]
              );
            }
          }
        }
        
        return await this.findById(id);
      }
      
      return null;
    } catch (error) {
      throw error;
    }
  }

  // Deny leave request
  static async deny(id, approvedBy) {
    try {
      const query = `
        UPDATE leaves
        SET status = 'denied', approvedBy = ?, updatedAt = ?
        WHERE id = ?
      `;
      
      const [result] = await pool.query(query, [approvedBy, new Date(), id]);
      
      if (result.affectedRows > 0) {
        return await this.findById(id);
      }
      
      return null;
    } catch (error) {
      throw error;
    }
  }

  // Get leave request with user details
  static async getLeaveWithDetails(id) {
    try {
      const query = `
        SELECT l.*, 
          u.name as userName, u.avatar as userAvatar,
          a.name as approverName, a.avatar as approverAvatar
        FROM leaves l
        JOIN users u ON l.userId = u.id
        LEFT JOIN users a ON l.approvedBy = a.id
        WHERE l.id = ?
      `;
      
      const [rows] = await pool.query(query, [id]);
      return rows.length ? rows[0] : null;
    } catch (error) {
      throw error;
    }
  }

  // Helper method to get all dates between two dates
  static getDatesBetween(startDate, endDate) {
    const dates = [];
    let currentDate = new Date(startDate);
    const end = new Date(endDate);
    
    while (currentDate <= end) {
      dates.push(currentDate.toISOString().split('T')[0]);
      currentDate.setDate(currentDate.getDate() + 1);
    }
    
    return dates;
  }
}

module.exports = Leave;
