import nodemailer from "nodemailer";
import dotenv from "dotenv";

dotenv.config();

// Create transporter based on environment
const createTransporter = () => {
  if (process.env.NODE_ENV === "production") {
    // Production email configuration (e.g., SendGrid, AWS SES, etc.)
    return nodemailer.createTransporter({
      service: process.env.EMAIL_SERVICE || "gmail",
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASSWORD,
      },
    });
  } else {
    // Development - use Ethereal Email for testing
    return nodemailer.createTransporter({
      host: "smtp.ethereal.email",
      port: 587,
      auth: {
        user: process.env.ETHEREAL_USER || "<EMAIL>",
        pass: process.env.ETHEREAL_PASS || "uazt kpba xjpp lsvp",
      },
    });
  }
};

// Send waitlist confirmation email
const sendWaitlistConfirmation = async (email) => {
  try {
    const transporter = createTransporter();

    const mailOptions = {
      from: process.env.EMAIL_FROM || "<EMAIL>",
      to: email,
      subject: "Welcome to TeamSzly Waitlist! 🎉",
      html: `
        <div style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #ffffff;">
          <!-- Header with Logo -->
          <div style="text-align: center; margin-bottom: 40px; padding: 30px 0; background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); border-radius: 12px;">
            <img src="https://teamszly.com/logo5.png" alt="TeamSzly Logo" style="height: 50px; width: auto; border-radius: 8px; margin-bottom: 15px;">
            <h1 style="color: #2c3e50; margin: 0; font-size: 28px; font-weight: 700;">Welcome to TeamSzly!</h1>
            <p style="color: #6c757d; font-size: 16px; margin: 8px 0 0 0;">You're now on our exclusive waitlist 🎉</p>
          </div>

          <!-- Main Content -->
          <div style="background: #ffffff; border: 2px solid #e9ecef; border-radius: 12px; padding: 30px; margin-bottom: 30px;">
            <h2 style="color: #2c3e50; margin-bottom: 20px; font-size: 22px;">What's Next?</h2>
            <div style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); padding: 25px; border-radius: 10px; margin-bottom: 25px;">
              <ul style="color: #495057; line-height: 1.8; margin: 0; padding-left: 20px; font-size: 15px;">
                <li style="margin-bottom: 8px;">🚀 <strong>Early Access:</strong> Be first to experience the future of team management</li>
                <li style="margin-bottom: 8px;">💰 <strong>Special Pricing:</strong> 50% off your first 3 months</li>
                <li style="margin-bottom: 8px;">🆓 <strong>Free Trial:</strong> 14 days of full access, no strings attached</li>
                <li style="margin-bottom: 8px;">📧 <strong>VIP Updates:</strong> Exclusive insights and launch notifications</li>
              </ul>
            </div>

            <div style="text-align: center; margin: 25px 0;">
              <h3 style="color: #2c3e50; margin-bottom: 15px; font-size: 20px;">Why Choose TeamSzly?</h3>
              <p style="color: #6c757d; line-height: 1.6; font-size: 15px; margin: 0;">
                <strong>Transform your team management experience.</strong><br>
                Get complete visibility into attendance, performance, and productivity with our intuitive dashboard.
                Streamline payroll, reduce meeting overhead, and boost team efficiency—all in one powerful platform.
              </p>
            </div>

            <div style="text-align: center; margin-top: 30px;">
              <a href="https://teamszly.com" style="display: inline-block; background: linear-gradient(135deg, #007bff 0%, #0056b3 100%); color: white; padding: 12px 30px; text-decoration: none; border-radius: 8px; font-weight: 600; font-size: 16px;">
                Visit Our Website →
              </a>
            </div>
          </div>

          <!-- Footer -->
          <div style="text-align: center; padding: 25px; background: #f8f9fa; border-radius: 8px; border-top: 3px solid #007bff;">
            <p style="color: #6c757d; margin: 0 0 10px 0; font-size: 14px;">
              Questions? Reply to this email or visit <a href="https://teamszly.com" style="color: #007bff; text-decoration: none;">teamszly.com</a><br>
              Thank you for joining us on this exciting journey! 🚀
            </p>
            <p style="color: #adb5bd; font-size: 12px; margin: 15px 0 0 0;">
              © ${new Date().getFullYear()} TeamSzly. All rights reserved.<br>
              <a href="https://teamszly.com" style="color: #6c757d; text-decoration: none;">teamszly.com</a>
            </p>
          </div>
        </div>
      `,
    };

    const result = await transporter.sendMail(mailOptions);
    console.log("Waitlist confirmation email sent:", result.messageId);
    return { success: true, messageId: result.messageId };
  } catch (error) {
    console.error("Error sending waitlist confirmation email:", error);
    return { success: false, error: error.message };
  }
};

export { sendWaitlistConfirmation };
