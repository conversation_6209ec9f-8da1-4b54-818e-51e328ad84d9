const { verifyToken } = require('../utils/jwt');
const { User } = require('../models');

// Authentication middleware
const authenticate = async (req, res, next) => {
  try {
    // Get token from Authorization header
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    const token = authHeader.split(' ')[1];
    const decoded = verifyToken(token);

    if (!decoded) {
      return res.status(401).json({ message: 'Invalid or expired token' });
    }

    // Verify user exists in database
    const user = await User.findByPk(decoded.id);
    if (!user) {
      return res.status(401).json({ message: 'User not found' });
    }

    // Add user info to request object
    req.user = decoded;
    next();
  } catch (error) {
    return res.status(401).json({ message: 'Authentication failed' });
  }
};

// Role-based authorization middleware
const authorize = (roles = []) => {
  if (typeof roles === 'string') {
    roles = [roles];
  }

  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    if (roles.length && !roles.includes(req.user.role)) {
      return res.status(403).json({ message: 'Forbidden: Insufficient permissions' });
    }

    next();
  };
};

// Company access middleware
const companyAccess = (req, res, next) => {
  try {
    const companyId = req.params.id || req.body.companyId;

    if (!companyId) {
      return next();
    }

    // If user doesn't have a company yet, they can't access other companies
    if (!req.user.companyId) {
      return res.status(403).json({ message: 'Forbidden: You must join a company first' });
    }

    // Allow access if user is admin or if the company matches the user's company
    if (req.user.role === 'admin' || req.user.companyId === companyId) {
      return next();
    }

    return res.status(403).json({ message: 'Forbidden: No access to this company' });
  } catch (error) {
    return res.status(500).json({ message: 'Server error' });
  }
};

module.exports = {
  authenticate,
  authorize,
  companyAccess
};
