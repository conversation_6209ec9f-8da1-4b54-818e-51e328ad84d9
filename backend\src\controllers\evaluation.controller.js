const { sequelize } = require('../config/database');

// We'll need to create the Evaluation model first, let's create a basic implementation
const Evaluation = {
  // Placeholder methods - we'll implement these with Sequelize models
  findAll: async (filters) => {
    // TODO: Implement with Sequelize
    return [];
  },
  
  findByPk: async (id) => {
    // TODO: Implement with Sequelize
    return null;
  },
  
  create: async (data) => {
    // TODO: Implement with Sequelize
    return data;
  },
  
  update: async (id, data) => {
    // TODO: Implement with Sequelize
    return true;
  },
  
  destroy: async (id) => {
    // TODO: Implement with Sequelize
    return true;
  },
  
  complete: async (id, evaluationData) => {
    // TODO: Implement with Sequelize
    return true;
  },
  
  getEvaluationWithDetails: async (id) => {
    // TODO: Implement with Sequelize
    return null;
  }
};

// Get all evaluations
const getEvaluations = async (req, res) => {
  try {
    const { companyId, type, employeeId, evaluatorId, status } = req.query;
    
    // Build filter object
    const filters = {};
    
    // If user is not admin, only show evaluations from their company
    if (req.user.role !== 'admin') {
      filters.companyId = req.user.companyId;
      
      // Regular employees can only see their own evaluations
      if (req.user.role === 'employee') {
        filters.employeeId = req.user.id;
      }
    } else if (companyId) {
      filters.companyId = companyId;
    }
    
    if (type) filters.type = type;
    if (employeeId && (req.user.role === 'admin' || req.user.role === 'manager')) {
      filters.employeeId = employeeId;
    }
    if (evaluatorId && (req.user.role === 'admin' || req.user.role === 'manager')) {
      filters.evaluatorId = evaluatorId;
    }
    if (status) filters.status = status;
    
    const evaluations = await Evaluation.findAll(filters);
    
    res.status(200).json({
      success: true,
      data: evaluations
    });
  } catch (error) {
    console.error('Get evaluations error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch evaluations'
    });
  }
};

// Get evaluation by ID
const getEvaluationById = async (req, res) => {
  try {
    const { id } = req.params;
    
    const evaluation = await Evaluation.getEvaluationWithDetails(id);
    
    if (!evaluation) {
      return res.status(404).json({
        success: false,
        message: 'Evaluation not found'
      });
    }
    
    // Check if user has access to this evaluation
    const canView = req.user.role === 'admin' ||
                   (req.user.role === 'manager' && req.user.companyId === evaluation.companyId) ||
                   evaluation.employeeId === req.user.id ||
                   evaluation.evaluatorId === req.user.id;
    
    if (!canView) {
      return res.status(403).json({
        success: false,
        message: 'Access denied to this evaluation'
      });
    }
    
    res.status(200).json({
      success: true,
      data: evaluation
    });
  } catch (error) {
    console.error('Get evaluation by ID error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch evaluation'
    });
  }
};

// Create new evaluation
const createEvaluation = async (req, res) => {
  try {
    const { companyId, type, employeeId, evaluatorId, projectId, date } = req.body;
    
    // Validate required fields
    if (!companyId || !type || !employeeId || !evaluatorId) {
      return res.status(400).json({
        success: false,
        message: 'Company ID, type, employee ID, and evaluator ID are required'
      });
    }
    
    // Check if user has permission to create evaluations
    const canCreate = req.user.role === 'admin' ||
                     (req.user.role === 'manager' && req.user.companyId === companyId) ||
                     req.user.id === evaluatorId;
    
    if (!canCreate) {
      return res.status(403).json({
        success: false,
        message: 'Insufficient permissions to create evaluations'
      });
    }
    
    // Validate type
    if (!['periodic', 'project'].includes(type)) {
      return res.status(400).json({
        success: false,
        message: 'Type must be either "periodic" or "project"'
      });
    }
    
    // If type is project, projectId is required
    if (type === 'project' && !projectId) {
      return res.status(400).json({
        success: false,
        message: 'Project ID is required for project evaluations'
      });
    }
    
    // Create evaluation
    const evaluation = await Evaluation.create({
      companyId,
      type,
      employeeId,
      evaluatorId,
      projectId,
      date: date || new Date().toISOString().split('T')[0],
      status: 'in progress'
    });
    
    res.status(201).json({
      success: true,
      data: evaluation,
      message: 'Evaluation created successfully'
    });
  } catch (error) {
    console.error('Create evaluation error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create evaluation'
    });
  }
};

// Update evaluation
const updateEvaluation = async (req, res) => {
  try {
    const { id } = req.params;
    const { 
      performanceRating, 
      teamworkRating, 
      communicationRating, 
      initiativeRating, 
      reliabilityRating, 
      feedback 
    } = req.body;
    
    const evaluation = await Evaluation.findByPk(id);
    
    if (!evaluation) {
      return res.status(404).json({
        success: false,
        message: 'Evaluation not found'
      });
    }
    
    // Check permissions - only evaluator can update
    if (evaluation.evaluatorId !== req.user.id && req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Only the evaluator can update this evaluation'
      });
    }
    
    // Can only update in progress evaluations
    if (evaluation.status !== 'in progress') {
      return res.status(400).json({
        success: false,
        message: 'Can only update evaluations that are in progress'
      });
    }
    
    // Validate ratings (1-5 scale)
    const ratings = { performanceRating, teamworkRating, communicationRating, initiativeRating, reliabilityRating };
    for (const [key, value] of Object.entries(ratings)) {
      if (value !== undefined && (value < 1 || value > 5)) {
        return res.status(400).json({
          success: false,
          message: `${key} must be between 1 and 5`
        });
      }
    }
    
    // Update evaluation
    await Evaluation.update(id, {
      performanceRating: performanceRating !== undefined ? performanceRating : evaluation.performanceRating,
      teamworkRating: teamworkRating !== undefined ? teamworkRating : evaluation.teamworkRating,
      communicationRating: communicationRating !== undefined ? communicationRating : evaluation.communicationRating,
      initiativeRating: initiativeRating !== undefined ? initiativeRating : evaluation.initiativeRating,
      reliabilityRating: reliabilityRating !== undefined ? reliabilityRating : evaluation.reliabilityRating,
      feedback: feedback !== undefined ? feedback : evaluation.feedback
    });
    
    // Get updated evaluation
    const updatedEvaluation = await Evaluation.getEvaluationWithDetails(id);
    
    res.status(200).json({
      success: true,
      data: updatedEvaluation,
      message: 'Evaluation updated successfully'
    });
  } catch (error) {
    console.error('Update evaluation error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update evaluation'
    });
  }
};

// Delete evaluation
const deleteEvaluation = async (req, res) => {
  try {
    const { id } = req.params;
    
    const evaluation = await Evaluation.findByPk(id);
    
    if (!evaluation) {
      return res.status(404).json({
        success: false,
        message: 'Evaluation not found'
      });
    }
    
    // Check permissions - only admin or evaluator can delete
    const canDelete = req.user.role === 'admin' ||
                     evaluation.evaluatorId === req.user.id ||
                     (req.user.role === 'manager' && req.user.companyId === evaluation.companyId);
    
    if (!canDelete) {
      return res.status(403).json({
        success: false,
        message: 'Insufficient permissions to delete this evaluation'
      });
    }
    
    // Can only delete in progress evaluations
    if (evaluation.status !== 'in progress') {
      return res.status(400).json({
        success: false,
        message: 'Can only delete evaluations that are in progress'
      });
    }
    
    await Evaluation.destroy(id);
    
    res.status(200).json({
      success: true,
      message: 'Evaluation deleted successfully'
    });
  } catch (error) {
    console.error('Delete evaluation error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete evaluation'
    });
  }
};

// Complete evaluation
const completeEvaluation = async (req, res) => {
  try {
    const { id } = req.params;
    const { 
      performanceRating, 
      teamworkRating, 
      communicationRating, 
      initiativeRating, 
      reliabilityRating, 
      feedback,
      goals 
    } = req.body;
    
    const evaluation = await Evaluation.findByPk(id);
    
    if (!evaluation) {
      return res.status(404).json({
        success: false,
        message: 'Evaluation not found'
      });
    }
    
    // Check permissions - only evaluator can complete
    if (evaluation.evaluatorId !== req.user.id && req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Only the evaluator can complete this evaluation'
      });
    }
    
    // Can only complete in progress evaluations
    if (evaluation.status !== 'in progress') {
      return res.status(400).json({
        success: false,
        message: 'Can only complete evaluations that are in progress'
      });
    }
    
    // Validate required ratings for completion
    if (!performanceRating || !teamworkRating || !communicationRating || !initiativeRating || !reliabilityRating) {
      return res.status(400).json({
        success: false,
        message: 'All ratings are required to complete the evaluation'
      });
    }
    
    // Validate ratings (1-5 scale)
    const ratings = { performanceRating, teamworkRating, communicationRating, initiativeRating, reliabilityRating };
    for (const [key, value] of Object.entries(ratings)) {
      if (value < 1 || value > 5) {
        return res.status(400).json({
          success: false,
          message: `${key} must be between 1 and 5`
        });
      }
    }
    
    // Complete evaluation
    const evaluationData = {
      performanceRating,
      teamworkRating,
      communicationRating,
      initiativeRating,
      reliabilityRating,
      feedback,
      goals: goals || []
    };
    
    await Evaluation.complete(id, evaluationData);
    
    // Get completed evaluation
    const completedEvaluation = await Evaluation.getEvaluationWithDetails(id);
    
    res.status(200).json({
      success: true,
      data: completedEvaluation,
      message: 'Evaluation completed successfully'
    });
  } catch (error) {
    console.error('Complete evaluation error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to complete evaluation'
    });
  }
};

module.exports = {
  getEvaluations,
  getEvaluationById,
  createEvaluation,
  updateEvaluation,
  deleteEvaluation,
  completeEvaluation
};
