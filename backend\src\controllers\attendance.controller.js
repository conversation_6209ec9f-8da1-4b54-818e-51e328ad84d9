const { sequelize } = require('../config/database');

// We'll need to create the Attendance model first, let's create a basic implementation
const Attendance = {
  // Placeholder methods - we'll implement these with Sequelize models
  findAll: async (filters) => {
    // TODO: Implement with Sequelize
    return [];
  },
  
  findByUserAndDate: async (userId, date) => {
    // TODO: Implement with Sequelize
    return null;
  },
  
  checkIn: async (companyId, userId, message) => {
    // TODO: Implement with Sequelize
    return { id: 'attendance-id', checkInTime: new Date(), checkInMessage: message };
  },
  
  checkOut: async (companyId, userId, message, projectHours) => {
    // TODO: Implement with Sequelize
    return { id: 'attendance-id', checkOutTime: new Date(), checkOutMessage: message };
  },
  
  getDailyOverview: async (companyId, date) => {
    // TODO: Implement with Sequelize
    return {
      date,
      summary: { totalUsers: 0, presentCount: 0, absentCount: 0, lateCount: 0, onLeaveCount: 0 },
      records: []
    };
  },
  
  getHourMap: async (userId, startDate, endDate) => {
    // TODO: Implement with Sequelize
    return [];
  }
};

// Get attendance records
const getAttendance = async (req, res) => {
  try {
    const { companyId, userId, date, startDate, endDate, status } = req.query;
    
    // Build filter object
    const filters = {};
    
    // If user is not admin, only show attendance from their company
    if (req.user.role !== 'admin') {
      filters.companyId = req.user.companyId;
      
      // Regular employees can only see their own attendance
      if (req.user.role === 'employee') {
        filters.userId = req.user.id;
      }
    } else if (companyId) {
      filters.companyId = companyId;
    }
    
    if (userId && (req.user.role === 'admin' || req.user.role === 'manager')) {
      filters.userId = userId;
    }
    if (date) filters.date = date;
    if (startDate) filters.startDate = startDate;
    if (endDate) filters.endDate = endDate;
    if (status) filters.status = status;
    
    const attendance = await Attendance.findAll(filters);
    
    res.status(200).json({
      success: true,
      data: attendance
    });
  } catch (error) {
    console.error('Get attendance error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch attendance records'
    });
  }
};

// Record check-in
const checkIn = async (req, res) => {
  try {
    const { companyId, checkInMessage } = req.body;
    const userId = req.user.id;
    
    // Validate required fields
    if (!companyId) {
      return res.status(400).json({
        success: false,
        message: 'Company ID is required'
      });
    }
    
    // Check if user has access to this company
    if (req.user.role !== 'admin' && req.user.companyId !== companyId) {
      return res.status(403).json({
        success: false,
        message: 'Access denied to check in for this company'
      });
    }
    
    // Check if user already checked in today
    const today = new Date().toISOString().split('T')[0];
    const existingAttendance = await Attendance.findByUserAndDate(userId, today);
    
    if (existingAttendance && existingAttendance.checkInTime) {
      return res.status(400).json({
        success: false,
        message: 'You have already checked in today'
      });
    }
    
    // Record check-in
    const attendance = await Attendance.checkIn(companyId, userId, checkInMessage);
    
    res.status(200).json({
      success: true,
      data: attendance,
      message: 'Check-in recorded successfully'
    });
  } catch (error) {
    console.error('Check-in error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to record check-in'
    });
  }
};

// Record check-out
const checkOut = async (req, res) => {
  try {
    const { companyId, checkOutMessage, projectHours } = req.body;
    const userId = req.user.id;
    
    // Validate required fields
    if (!companyId) {
      return res.status(400).json({
        success: false,
        message: 'Company ID is required'
      });
    }
    
    // Check if user has access to this company
    if (req.user.role !== 'admin' && req.user.companyId !== companyId) {
      return res.status(403).json({
        success: false,
        message: 'Access denied to check out for this company'
      });
    }
    
    // Check if user has checked in today
    const today = new Date().toISOString().split('T')[0];
    const existingAttendance = await Attendance.findByUserAndDate(userId, today);
    
    if (!existingAttendance || !existingAttendance.checkInTime) {
      return res.status(400).json({
        success: false,
        message: 'You must check in before checking out'
      });
    }
    
    if (existingAttendance.checkOutTime) {
      return res.status(400).json({
        success: false,
        message: 'You have already checked out today'
      });
    }
    
    // Validate project hours if provided
    if (projectHours && Array.isArray(projectHours)) {
      const totalHours = projectHours.reduce((sum, ph) => sum + (ph.hours || 0), 0);
      
      // Calculate work hours
      const checkInTime = new Date(existingAttendance.checkInTime);
      const now = new Date();
      const workHours = (now - checkInTime) / (1000 * 60 * 60);
      
      if (totalHours > workHours + 1) { // Allow 1 hour buffer
        return res.status(400).json({
          success: false,
          message: 'Total project hours cannot exceed work hours'
        });
      }
    }
    
    // Record check-out
    const attendance = await Attendance.checkOut(companyId, userId, checkOutMessage, projectHours);
    
    res.status(200).json({
      success: true,
      data: attendance,
      message: 'Check-out recorded successfully'
    });
  } catch (error) {
    console.error('Check-out error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to record check-out'
    });
  }
};

// Get daily overview
const getDailyOverview = async (req, res) => {
  try {
    const { date } = req.query;
    const companyId = req.user.companyId;
    
    // Only admin and managers can view daily overview
    if (req.user.role === 'employee') {
      return res.status(403).json({
        success: false,
        message: 'Insufficient permissions to view daily overview'
      });
    }
    
    if (!companyId) {
      return res.status(400).json({
        success: false,
        message: 'User must belong to a company to view daily overview'
      });
    }
    
    const targetDate = date || new Date().toISOString().split('T')[0];
    const overview = await Attendance.getDailyOverview(companyId, targetDate);
    
    res.status(200).json({
      success: true,
      data: overview
    });
  } catch (error) {
    console.error('Get daily overview error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch daily overview'
    });
  }
};

// Get detailed logs
const getDetailedLogs = async (req, res) => {
  try {
    const { startDate, endDate, userId } = req.query;
    const companyId = req.user.companyId;
    
    // Build filter object
    const filters = { companyId };
    
    // Regular employees can only see their own logs
    if (req.user.role === 'employee') {
      filters.userId = req.user.id;
    } else if (userId) {
      filters.userId = userId;
    }
    
    if (startDate) filters.startDate = startDate;
    if (endDate) filters.endDate = endDate;
    
    const logs = await Attendance.findAll(filters);
    
    res.status(200).json({
      success: true,
      data: logs
    });
  } catch (error) {
    console.error('Get detailed logs error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch detailed logs'
    });
  }
};

// Get hours breakdown
const getHourMap = async (req, res) => {
  try {
    const { startDate, endDate, userId } = req.query;
    
    // Determine target user
    let targetUserId = req.user.id;
    
    // Admin and managers can view other users' hour maps
    if (userId && (req.user.role === 'admin' || req.user.role === 'manager')) {
      targetUserId = userId;
    }
    
    // Default to current month if no dates provided
    const now = new Date();
    const defaultStartDate = new Date(now.getFullYear(), now.getMonth(), 1).toISOString().split('T')[0];
    const defaultEndDate = new Date(now.getFullYear(), now.getMonth() + 1, 0).toISOString().split('T')[0];
    
    const hourMap = await Attendance.getHourMap(
      targetUserId,
      startDate || defaultStartDate,
      endDate || defaultEndDate
    );
    
    res.status(200).json({
      success: true,
      data: hourMap
    });
  } catch (error) {
    console.error('Get hour map error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch hours breakdown'
    });
  }
};

module.exports = {
  getAttendance,
  checkIn,
  checkOut,
  getDailyOverview,
  getDetailedLogs,
  getHourMap
};
