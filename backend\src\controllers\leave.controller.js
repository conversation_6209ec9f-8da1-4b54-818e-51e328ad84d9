const { sequelize } = require('../config/database');

// We'll need to create the Leave model first, let's create a basic implementation
const Leave = {
  // Placeholder methods - we'll implement these with Sequelize models
  findAll: async (filters) => {
    // TODO: Implement with Sequelize
    return [];
  },
  
  findByPk: async (id) => {
    // TODO: Implement with Sequelize
    return null;
  },
  
  create: async (data) => {
    // TODO: Implement with Sequelize
    return data;
  },
  
  update: async (id, data) => {
    // TODO: Implement with Sequelize
    return true;
  },
  
  destroy: async (id) => {
    // TODO: Implement with Sequelize
    return true;
  },
  
  approve: async (id, approvedBy) => {
    // TODO: Implement with Sequelize
    return true;
  },
  
  deny: async (id, approvedBy, reason) => {
    // TODO: Implement with Sequelize
    return true;
  },
  
  getLeaveWithDetails: async (id) => {
    // TODO: Implement with Sequelize
    return null;
  }
};

// Get all leave requests
const getLeaves = async (req, res) => {
  try {
    const { companyId, status, startDate, endDate, userId } = req.query;
    
    // Build filter object
    const filters = {};
    
    // If user is not admin, only show leaves from their company
    if (req.user.role !== 'admin') {
      filters.companyId = req.user.companyId;
      
      // Regular employees can only see their own leaves
      if (req.user.role === 'employee') {
        filters.userId = req.user.id;
      }
    } else if (companyId) {
      filters.companyId = companyId;
    }
    
    if (status) filters.status = status;
    if (startDate) filters.startDate = startDate;
    if (endDate) filters.endDate = endDate;
    if (userId && (req.user.role === 'admin' || req.user.role === 'manager')) {
      filters.userId = userId;
    }
    
    const leaves = await Leave.findAll(filters);
    
    res.status(200).json({
      success: true,
      data: leaves
    });
  } catch (error) {
    console.error('Get leaves error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch leave requests'
    });
  }
};

// Get leave request by ID
const getLeaveById = async (req, res) => {
  try {
    const { id } = req.params;
    
    const leave = await Leave.getLeaveWithDetails(id);
    
    if (!leave) {
      return res.status(404).json({
        success: false,
        message: 'Leave request not found'
      });
    }
    
    // Check if user has access to this leave request
    const canView = req.user.role === 'admin' ||
                   (req.user.role === 'manager' && req.user.companyId === leave.companyId) ||
                   leave.userId === req.user.id;
    
    if (!canView) {
      return res.status(403).json({
        success: false,
        message: 'Access denied to this leave request'
      });
    }
    
    res.status(200).json({
      success: true,
      data: leave
    });
  } catch (error) {
    console.error('Get leave by ID error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch leave request'
    });
  }
};

// Create new leave request
const createLeave = async (req, res) => {
  try {
    const { companyId, startDate, endDate, reason } = req.body;
    const userId = req.user.id;
    
    // Validate required fields
    if (!companyId || !startDate || !endDate) {
      return res.status(400).json({
        success: false,
        message: 'Company ID, start date, and end date are required'
      });
    }
    
    // Check if user has access to this company
    if (req.user.role !== 'admin' && req.user.companyId !== companyId) {
      return res.status(403).json({
        success: false,
        message: 'Access denied to create leave request for this company'
      });
    }
    
    // Validate dates
    const start = new Date(startDate);
    const end = new Date(endDate);
    
    if (start >= end) {
      return res.status(400).json({
        success: false,
        message: 'End date must be after start date'
      });
    }
    
    if (start < new Date()) {
      return res.status(400).json({
        success: false,
        message: 'Start date cannot be in the past'
      });
    }
    
    // Create leave request
    const leave = await Leave.create({
      companyId,
      userId,
      startDate,
      endDate,
      reason,
      status: 'pending'
    });
    
    res.status(201).json({
      success: true,
      data: leave,
      message: 'Leave request created successfully'
    });
  } catch (error) {
    console.error('Create leave error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create leave request'
    });
  }
};

// Update leave request
const updateLeave = async (req, res) => {
  try {
    const { id } = req.params;
    const { startDate, endDate, reason } = req.body;
    
    const leave = await Leave.findByPk(id);
    
    if (!leave) {
      return res.status(404).json({
        success: false,
        message: 'Leave request not found'
      });
    }
    
    // Check permissions - only the requester can update their own pending leave
    if (leave.userId !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'You can only update your own leave requests'
      });
    }
    
    // Can only update pending leaves
    if (leave.status !== 'pending') {
      return res.status(400).json({
        success: false,
        message: 'Can only update pending leave requests'
      });
    }
    
    // Validate dates if provided
    if (startDate && endDate) {
      const start = new Date(startDate);
      const end = new Date(endDate);
      
      if (start >= end) {
        return res.status(400).json({
          success: false,
          message: 'End date must be after start date'
        });
      }
      
      if (start < new Date()) {
        return res.status(400).json({
          success: false,
          message: 'Start date cannot be in the past'
        });
      }
    }
    
    // Update leave request
    await Leave.update(id, {
      startDate: startDate || leave.startDate,
      endDate: endDate || leave.endDate,
      reason: reason !== undefined ? reason : leave.reason
    });
    
    // Get updated leave
    const updatedLeave = await Leave.getLeaveWithDetails(id);
    
    res.status(200).json({
      success: true,
      data: updatedLeave,
      message: 'Leave request updated successfully'
    });
  } catch (error) {
    console.error('Update leave error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update leave request'
    });
  }
};

// Approve leave request
const approveLeave = async (req, res) => {
  try {
    const { id } = req.params;
    const approvedBy = req.user.id;
    
    const leave = await Leave.findByPk(id);
    
    if (!leave) {
      return res.status(404).json({
        success: false,
        message: 'Leave request not found'
      });
    }
    
    // Check permissions - only admin or manager can approve
    const canApprove = req.user.role === 'admin' ||
                      (req.user.role === 'manager' && req.user.companyId === leave.companyId);
    
    if (!canApprove) {
      return res.status(403).json({
        success: false,
        message: 'Insufficient permissions to approve leave requests'
      });
    }
    
    // Can only approve pending leaves
    if (leave.status !== 'pending') {
      return res.status(400).json({
        success: false,
        message: 'Can only approve pending leave requests'
      });
    }
    
    // Approve leave
    await Leave.approve(id, approvedBy);
    
    // Get updated leave
    const updatedLeave = await Leave.getLeaveWithDetails(id);
    
    res.status(200).json({
      success: true,
      data: updatedLeave,
      message: 'Leave request approved successfully'
    });
  } catch (error) {
    console.error('Approve leave error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to approve leave request'
    });
  }
};

// Deny leave request
const denyLeave = async (req, res) => {
  try {
    const { id } = req.params;
    const { reason } = req.body;
    const approvedBy = req.user.id;
    
    const leave = await Leave.findByPk(id);
    
    if (!leave) {
      return res.status(404).json({
        success: false,
        message: 'Leave request not found'
      });
    }
    
    // Check permissions - only admin or manager can deny
    const canDeny = req.user.role === 'admin' ||
                   (req.user.role === 'manager' && req.user.companyId === leave.companyId);
    
    if (!canDeny) {
      return res.status(403).json({
        success: false,
        message: 'Insufficient permissions to deny leave requests'
      });
    }
    
    // Can only deny pending leaves
    if (leave.status !== 'pending') {
      return res.status(400).json({
        success: false,
        message: 'Can only deny pending leave requests'
      });
    }
    
    // Deny leave
    await Leave.deny(id, approvedBy, reason);
    
    // Get updated leave
    const updatedLeave = await Leave.getLeaveWithDetails(id);
    
    res.status(200).json({
      success: true,
      data: updatedLeave,
      message: 'Leave request denied successfully'
    });
  } catch (error) {
    console.error('Deny leave error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to deny leave request'
    });
  }
};

module.exports = {
  getLeaves,
  getLeaveById,
  createLeave,
  updateLeave,
  approveLeave,
  denyLeave
};
