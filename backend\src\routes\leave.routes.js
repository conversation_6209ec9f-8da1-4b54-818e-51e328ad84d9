const express = require('express');
const router = express.Router();
const leaveController = require('../controllers/leave.controller');
const { authenticate, authorize } = require('../middleware/auth.middleware');

// Get leave requests
router.get('/', authenticate, leaveController.getLeaves);

// Create leave request
router.post('/', authenticate, leaveController.createLeave);

// Get leave by ID
router.get('/:id', authenticate, leaveController.getLeaveById);

// Update leave request
router.put('/:id', authenticate, leaveController.updateLeave);

// Approve leave
router.put('/:id/approve', authenticate, authorize(['admin', 'manager']), leaveController.approveLeave);

// Deny leave
router.put('/:id/deny', authenticate, authorize(['admin', 'manager']), leaveController.denyLeave);

module.exports = router;
