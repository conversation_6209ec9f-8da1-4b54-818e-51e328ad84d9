const express = require('express');
const router = express.Router();
const userController = require('../controllers/user.controller');
const { authenticate, authorize } = require('../middleware/auth.middleware');

// Get all users
router.get('/', authenticate, userController.getUsers);

// Invite new user
router.post('/invite', authenticate, authorize(['admin', 'manager']), userController.inviteUser);

// Get users by company
router.get('/company/:companyId', authenticate, userController.getUsersByCompany);

// Get user by ID
router.get('/:id', authenticate, userController.getUserById);

// Update user
router.put('/:id', authenticate, userController.updateUser);

// Delete user
router.delete('/:id', authenticate, authorize(['admin', 'manager']), userController.deleteUser);

module.exports = router;
