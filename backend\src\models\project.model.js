const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');
const { v4: uuidv4 } = require('uuid');

const Project = sequelize.define('Project', {
  id: {
    type: DataTypes.STRING(36),
    primaryKey: true,
    defaultValue: () => uuidv4()
  },
  companyId: {
    type: DataTypes.STRING(36),
    allowNull: false
  },
  title: {
    type: DataTypes.STRING(255),
    allowNull: false
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  createdBy: {
    type: DataTypes.STRING(36),
    allowNull: false
  },
  dueDate: {
    type: DataTypes.DATE,
    allowNull: true
  },
  status: {
    type: DataTypes.ENUM('not started', 'in progress', 'completed'),
    defaultValue: 'not started'
  },
  projectFormat: {
    type: DataTypes.ENUM('statuses', 'milestones'),
    defaultValue: 'statuses'
  }
}, {
  tableName: 'projects',
  timestamps: true,
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
});

// Define ProjectTeamMember model
const ProjectTeamMember = sequelize.define('ProjectTeamMember', {
  id: {
    type: DataTypes.STRING(36),
    primaryKey: true,
    defaultValue: () => uuidv4()
  },
  projectId: {
    type: DataTypes.STRING(36),
    allowNull: false
  },
  userId: {
    type: DataTypes.STRING(36),
    allowNull: false
  },
  role: {
    type: DataTypes.STRING(255),
    allowNull: true
  }
}, {
  tableName: 'project_team_members',
  timestamps: false
});

// Define ProjectColumn model
const ProjectColumn = sequelize.define('ProjectColumn', {
  id: {
    type: DataTypes.STRING(36),
    primaryKey: true,
    defaultValue: () => uuidv4()
  },
  projectId: {
    type: DataTypes.STRING(36),
    allowNull: false
  },
  title: {
    type: DataTypes.STRING(255),
    allowNull: false
  },
  color: {
    type: DataTypes.STRING(7),
    allowNull: true
  }
}, {
  tableName: 'project_columns',
  timestamps: false
});

// Define ProjectMilestone model
const ProjectMilestone = sequelize.define('ProjectMilestone', {
  id: {
    type: DataTypes.STRING(36),
    primaryKey: true,
    defaultValue: () => uuidv4()
  },
  projectId: {
    type: DataTypes.STRING(36),
    allowNull: false
  },
  title: {
    type: DataTypes.STRING(255),
    allowNull: false
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  dueDate: {
    type: DataTypes.DATE,
    allowNull: true
  },
  status: {
    type: DataTypes.ENUM('not started', 'in progress', 'completed'),
    defaultValue: 'not started'
  },
  completedAt: {
    type: DataTypes.DATE,
    allowNull: true
  }
}, {
  tableName: 'project_milestones',
  timestamps: false
});

// Define associations
Project.hasMany(ProjectTeamMember, { foreignKey: 'projectId' });
Project.hasMany(ProjectColumn, { foreignKey: 'projectId' });
Project.hasMany(ProjectMilestone, { foreignKey: 'projectId' });

// Get projects for a user (including ones they created and are members of)
Project.findForUser = async function(userId) {
  const { Op } = require('sequelize');

  return await Project.findAll({
    where: {
      [Op.or]: [
        { createdBy: userId },
        { '$ProjectTeamMembers.userId$': userId }
      ]
    },
    include: [{
      model: ProjectTeamMember,
      required: false
    }],
    order: [['createdAt', 'DESC']]
  });
};

// Get project team members
Project.getTeamMembers = async function(projectId) {
  const User = require('./user.model');

  const teamMembers = await ProjectTeamMember.findAll({
    where: { projectId },
    include: [{
      model: User,
      attributes: ['id', 'name', 'email', 'position', 'avatar']
    }]
  });

  return teamMembers.map(member => ({
    ...member.User.dataValues,
    projectRole: member.role
  }));
};

// Add team member to project
Project.addTeamMember = async function(projectId, userId, role = null) {
  return await ProjectTeamMember.create({
    projectId,
    userId,
    role
  });
};

// Remove team member from project
Project.removeTeamMember = async function(projectId, userId) {
  return await ProjectTeamMember.destroy({
    where: {
      projectId,
      userId
    }
  });
};

// Get project with details (including team members, columns, milestones)
Project.getProjectWithDetails = async function(projectId) {
  const project = await Project.findByPk(projectId);
  if (!project) return null;

  const teamMembers = await this.getTeamMembers(projectId);

  let columns = [];
  let milestones = [];

  if (project.projectFormat === 'statuses') {
    columns = await ProjectColumn.findAll({
      where: { projectId }
    });
  }

  if (project.projectFormat === 'milestones') {
    milestones = await ProjectMilestone.findAll({
      where: { projectId }
    });
  }

  return {
    ...project.toJSON(),
    teamMembers,
    columns,
    milestones
  };
};

module.exports = {
  Project,
  ProjectTeamMember,
  ProjectColumn,
  ProjectMilestone
};
