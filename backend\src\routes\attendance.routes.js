const express = require('express');
const router = express.Router();
const attendanceController = require('../controllers/attendance.controller');
const { authenticate, authorize } = require('../middleware/auth.middleware');

// Get attendance records
router.get('/', authenticate, attendanceController.getAttendance);

// Record check-in
router.post('/check-in', authenticate, attendanceController.checkIn);

// Record check-out
router.post('/check-out', authenticate, attendanceController.checkOut);

// Get daily overview
router.get('/daily', authenticate, authorize(['admin', 'manager']), attendanceController.getDailyOverview);

// Get detailed logs
router.get('/logs', authenticate, attendanceController.getDetailedLogs);

// Get hours breakdown
router.get('/hourmap', authenticate, attendanceController.getHourMap);

module.exports = router;
